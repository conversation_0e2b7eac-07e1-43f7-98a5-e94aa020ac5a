package com.zenyte.plugins

import io.github.classgraph.ClassInfoList
import io.github.classgraph.ScanResult

/**
 * <AUTHOR>
 */
object AnnotatedPluginScanType : PluginScanType {

    override fun scan(result: ScanR<PERSON>ult, scanClass: Class<*>): ClassInfoList {
        if (!scanClass.isAnnotation) throw IllegalArgumentException()

        @Suppress("UNCHECKED_CAST")
        return result.getClassesWithAnnotation(scanClass as Class<out Annotation>)
    }

}