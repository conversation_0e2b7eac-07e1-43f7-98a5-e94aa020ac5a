package com.zenyte.plugins

import io.github.classgraph.ClassInfoList
import io.github.classgraph.ScanResult

/**
 * <AUTHOR>
 */
object ImplementingPluginScanType : PluginScanType {

    override fun scan(result: ScanR<PERSON>ult, scanClass: Class<*>): ClassInfoList =
        result.getClassesImplementing(scanClass)
            .filter { info -> !info.isInterface && !info.isAbstract && !info.isAnonymousInnerClass }

}