package com.near_reality.tools.logging.file

//@Suppress("unused")
//object GameLoggerFileModule {
//
//    @Subscribe
//    @JvmStatic
//    fun onServerLaunched(event: ServerLaunchEvent) =
//        GameLogger.onMessage("FileIO", GameLoggerFileAppender::writeMaybeFlush)
//
//    @Subscribe
//    @JvmStatic
//    fun onServerShutdown(event: ServerShutdownEvent) =
//        GameLoggerFileAppender.close()
//}
