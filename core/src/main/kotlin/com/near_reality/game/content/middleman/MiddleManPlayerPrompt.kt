package com.near_reality.game.content.middleman

import com.zenyte.game.item.Item
import com.zenyte.game.world.entity.player.Player
import com.zenyte.plugins.dialogue.PlainChat
import com.zenyte.plugins.dialogue.SkillDialogue

fun promptDPinSelectionAndOpenMiddlemanInterface(player: Player) {
    val donatorBondsInInventory: MutableList<Item> = ArrayList()
    for (itemId in MiddleManConstants.donatorBondItemIds) {
        val donatorBondItem = player.inventory.getAny(itemId)
        if (donatorBondItem != null) donatorBondsInInventory.add(donatorBondItem)
    }
    if (donatorBondsInInventory.isEmpty()) {
        player.dialogueManager.start(PlainChat(player, "You do not have any Donator Bonds in your inventory."))
        return
    }
    player.dialogueManager.start(object : SkillDialogue(
        player, "Select the Donator Bond you'd like to trade", *donatorBondsInInventory.toTypedArray()
    ) {
        override fun run(slotId: Int, amount: Int) {
            val selectedDonatorBondItem = donatorBondsInInventory[slotId]
            player.middleManController
                .openTradeRequestInterface(selectedDonatorBondItem.id, amount, "")
        }
    })
}
