package com.near_reality.game.content.bountyhunter

import com.zenyte.utils.TimeUnit

/**
 * This file contains all changeable vars for the Bounty Hunter system
 * (i.e. emblem values, point rewards)
 * <AUTHOR> / <PERSON><PERSON>
 */
object BountyHunterVars {


    /* Emblem Value in BH Points */
    const val T1_EMBLEM_AMOUNT = 2
    const val T2_EMBLEM_AMOUNT = 4
    const val T3_EMBLEM_AMOUNT = 7
    const val T4_EMBLEM_AMOUNT = 11
    const val T5_EMBLEM_AMOUNT = 16
    const val T6_EMBLEM_AMOUNT = 22
    const val T7_EMBLEM_AMOUNT = 29
    const val T8_EMBLEM_AMOUNT = 37
    const val T9_EMBLEM_AMOUNT = 46
    const val T10_EMBLEM_AMOUNT = 56

    /* Base Upgrade Chance to next tier, 20 = 20% */
    const val T1_UPGRADE_CHANCE = 70
    const val T2_UPGRADE_CHANCE = 60
    const val T3_UPGRADE_CHANCE = 50
    const val T4_UPGRADE_CHANCE = 45
    const val T5_UPGRADE_CHANCE = 40
    const val T6_UPGRADE_CHANCE = 38
    const val T7_UPGRADE_CHANCE = 36
    const val T8_UPGRADE_CHANCE = 34
    const val T9_UPGRADE_CHANCE = 32
    const val T10_UPGRADE_CHANCE = 30

    /* Interfaces IDs */
    const val I_PARENT = 1722
    const val I_MAXIMIZED_CONTAINER = 3
    const val I_MINIMIZE_BUTTON = 23
    const val I_MAXIMIZE_BUTTON = 24
    const val I_MINIMIZED_CONTAINER = 21
    const val I_CHILD_SHOW_HOTSPOT = 6
    const val I_CHILD_TARGET_BEST_EMBLEM = 7
    const val I_CHILD_TARGET_BEST_EMBLEM_LVL = 8
    const val I_CHILD_TARGET_MULTI_ZONE = 9
    const val I_CHILD_TARGET_WILDERNESS_LVL = 11
    const val I_CHILD_TARGET_NAME = 12
    const val I_CHILD_SKIP_BUTTON = 13
    const val I_CHILD_INFO_LABEL = 15
    const val I_CHILD_INFO_DATA = 16
    const val I_CHILD_SWITCH_BACKWARD = 17
    const val I_CHILD_SWITCH_FORWARD = 18

    /* Hotspot Settings */
    const val HOTSPOT_CYCLE_MAIN = 20
    const val HOTSPOT_CYCLE_DEV = 3

    /* Other Cycle Tasks */
    const val INTERFACE_UPDATE_INTERVAL = 30

    /* Sprite IDs */
    const val SKULL_T1 = -1
    const val SKULL_T2 = -1
    const val SKULL_T3 = -1
    const val SKULL_T4 = -1
    const val SKULL_T5 = -1
    const val SKULL_T6 = -1
    const val SKULL_T7 = -1
    const val SKULL_T8 = -1
    const val SKULL_T9 = -1
    const val SKULL_T10 = -1


}