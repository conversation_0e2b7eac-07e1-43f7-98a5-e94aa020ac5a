package com.near_reality.game.item;

import com.near_reality.cache_tool.packing.custom.CustomObjectsPacker;

/**
 * AUTO-GENERATED FILE FROM {@link CustomObjectsPacker}. DO NOT EDIT.
 */

public final class CustomObjectId
{
	public static final int OBSTACLE_NET = 16499;
	public static final int PESTLE_AND_MORTAR = 47567;
	public static final int PICKAXE = 47568;
	public static final int COMBAT_ESSENCE_LAVA_POOL = 59000;
	public static final int OBELISK = 59001;
	public static final int OBELISK_59002 = 59002;
	public static final int ORNATE_POOL_OF_REJUVENATION = 59003;
	public static final int FANCY_POOL_OF_REJUVENATION = 59004;
	public static final int OVERLOAD_POOL_OF_REJUVENATION = 59005;
	public static final int DIVINE_POOL_OF_REJUVENATION = 59006;
	public static final int PORTAL_NEXUS = 59007;
	public static final int EVENT_BOARD = 59008;
	public static final int WELL_OF_GOODWILL = 59009;
	public static final int AFK_AGILITY = 59010;
	public static final int AFK_FARMING = 59011;
	public static final int AFK_FIREMAKING = 59012;
	public static final int AFK_MINING = 59013;
	public static final int AFK_THIEVING = 59014;
	public static final int AFK_WOODCUTTING = 59015;
	public static final int AFK_CRAFTING = 59016;
	public static final int AFK_SMITHING = 59017;
	public static final int AFK_RUNECRAFTING = 59018;
	public static final int AFK_FISHING = 59019;
}
