package com.near_reality.plugins.area.osrs_home.npc

import com.near_reality.scripts.npc.actions.NPCActionScript
import com.zenyte.game.GameInterface
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.player.Player
import com.zenyte.game.world.entity.player.dialogue.dialogue
import com.zenyte.game.world.entity.player.dialogue.options

class EssencePerkMasterNPCAction : NPCActionScript() {

    init {
        npcs(NpcId.FIGHTER_11681)

        "Talk-to" {
            player.dialogue(npc) {
                options("Perk Master Options") {
                    dialogueOption("Buy Perks", true) {
                        GameInterface.ESSENCE_PERK_OVERVIEW.open(player)
                    }
                    dialogueOption("View Utility Tasks", true) {
                        viewChallenges(player)
                    }
                    dialogueOption("Item Sacrifice Values", true) {
                        GameInterface.COMBAT_ESSENCE_PRICES.open(player)
                    }
                    dialogueOption("What are perks?", false) {
                        npc("Exiles offers 2 perk systems, Combat and Utility.")
                        npc("Combat Perks are enhancements that take combat to new heights. Unlock abilities such as double hits, faster attacks, higher defence, and more!")
                        npc("Utility Perks are powerful QoL perks that will help all aspects of the game!")
                        player("How do I purchase the perks?")
                        npc("Each perk system uses their own currency, called Essence.")
                        npc("Combat Essence is gained by sacrificing valuable items to the Lava Pool right next to me.")
                        npc("Utility Essence is gained by completing skilling tasks and daily challenges.")
                    }

                }
            }
        }

        "Buy Combat Perks" {
            GameInterface.ESSENCE_PERK_OVERVIEW.open(player)
        }

        "View Utility Tasks" {
            viewChallenges(player)
        }

        "Item Values" {
            GameInterface.COMBAT_ESSENCE_PRICES.open(player)
        }
    }

    private fun viewChallenges(player: Player) {
        player.addTemporaryAttribute("daily_challenge_claimable", 1)
        GameInterface.ESSENCE_TASK_OVERVIEW.open(player)
    }

}